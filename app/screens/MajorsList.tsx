import React, { useEffect, useState } from "react";
import {
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Pressable,
  SafeAreaView,
  TouchableOpacity,
  Text,
  ScrollView,
  View,
  RefreshControl,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Major, majorService } from "@/services/majorService";
import { Link, router } from "expo-router";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import Svg, { Path } from "react-native-svg";
import { rtlStyle } from "@/utils/rtl";
// Icon Components
const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

export default function MajorsListScreen() {
  const [majors, setMajors] = useState<Major[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const colorScheme = useColorScheme() ?? "light";

  const handleBack = () => {
    router.back();
  };

  const loadMajors = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await majorService.getMajors();
      setMajors(data);
    } catch (err) {
      setError("Failed to fetch majors.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await majorService.getMajors();
      setMajors(data);
    } catch (err) {
      setError("Failed to refresh majors.");
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadMajors();
  }, []);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading majors...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: Colors[colorScheme].background },
      ]}
    >
      {/* Header */}
      <View
        style={[
          styles.header,
          { borderBottomColor: Colors[colorScheme].tabIconDefault + "30" },
        ]}
      >
        <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
          <BackArrowIcon color={Colors[colorScheme].text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: Colors[colorScheme].text }]}>
          تنظیمات
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#007bff']}
            tintColor="#007bff"
          />
        }
      >
        <ThemedView style={styles.container}>
          <ThemedText style={styles.title}>Majors</ThemedText>
          <Link href="/screens/MajorAdd" asChild>
            <Pressable style={styles.addButton}>
              <ThemedText style={styles.addButtonText}>
                Add New Major
              </ThemedText>
            </Pressable>
          </Link>
          {majors.length === 0 ? (
            <ThemedText style={styles.noDataText}>No majors found.</ThemedText>
          ) : (
            <FlatList
              data={majors}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <ThemedView style={styles.majorItem}>
                  <Link href={`/screens/MajorDetails?id=${item.id}`} asChild>
                    <Pressable>
                      <ThemedText style={styles.majorName}>
                        {item.name}
                      </ThemedText>
                    </Pressable>
                  </Link>
                  <ThemedView style={styles.actions}>
                    <Link href={`/screens/MajorEdit?id=${item.id}`} asChild>
                      <Pressable style={styles.actionButton}>
                        <ThemedText style={styles.actionButtonText}>
                          Edit
                        </ThemedText>
                      </Pressable>
                    </Link>
                    {/* Delete functionality would be implemented here */}
                  </ThemedView>
                </ThemedView>
              )}
            />
          )}
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    fontFamily: "Vazirmatn",
    textAlign: "center",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: "#007bff",
    padding: 10,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 16,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  majorItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
  },
  majorName: {
    fontSize: 18,
  },
  actions: {
    flexDirection: "row",
  },
  actionButton: {
    backgroundColor: "#28a745",
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: "#fff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: "center",
    marginTop: 20,
  },
});
