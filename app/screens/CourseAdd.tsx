import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { courseService } from '@/services/courseService';
import { router } from 'expo-router';

export default function CourseAddScreen() {
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddCourse = async () => {
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Course name cannot be empty.');
      return;
    }

    setLoading(true);
    try {
      await courseService.createCourse({ name });
      Alert.alert('Success', 'Course added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add course. Please try again.');
      console.error('Failed to add course:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Add New Course</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Course Name"
        value={name}
        onChangeText={setName}
        editable={!loading}
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddCourse}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'Adding...' : 'Add Course'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
