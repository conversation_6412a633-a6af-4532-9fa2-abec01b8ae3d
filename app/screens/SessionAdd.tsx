import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { sessionService } from '@/services/sessionService';
import { router } from 'expo-router';

export default function SessionAddScreen() {
  const [userId, setUserId] = useState('');
  const [examId, setExamId] = useState('');
  const [score, setScore] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddSession = async () => {
    if (!userId.trim() || !examId.trim() || !score.trim()) {
      Alert.alert('Validation Error', 'User ID, Exam ID, and Score cannot be empty.');
      return;
    }

    setLoading(true);
    try {
      // Assuming your CreateSessionDto has 'userId', 'examId', and 'score' fields
      await sessionService.createSession({ userId, examId, score: parseInt(score) });
      Alert.alert('Success', 'Session added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add session. Please try again.');
      console.error('Failed to add session:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Add New Session</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="User ID"
        value={userId}
        onChangeText={setUserId}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="Exam ID"
        value={examId}
        onChangeText={setExamId}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="Score"
        value={score}
        onChangeText={setScore}
        editable={!loading}
        keyboardType="numeric"
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddSession}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'Adding...' : 'Add Session'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
