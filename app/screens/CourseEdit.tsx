import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Course, courseService } from '@/services/courseService';
import { useLocalSearchParams, router } from 'expo-router';

export default function CourseEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchCourse = async () => {
        try {
          const data = await courseService.getCourse(id as string);
          setName(data.name);
        } catch (err) {
          setError('Failed to fetch course details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchCourse();
    } else {
      setError('Course ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateCourse = async () => {
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Course name cannot be empty.');
      return;
    }
    if (!id) {
      Alert.alert('Error', 'Course ID is missing.');
      return;
    }

    setSaving(true);
    try {
      await courseService.updateCourse(id as string, { name });
      Alert.alert('Success', 'Course updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update course. Please try again.');
      console.error('Failed to update course:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading course for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Edit Course</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Course Name"
        value={name}
        onChangeText={setName}
        editable={!saving}
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateCourse}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
