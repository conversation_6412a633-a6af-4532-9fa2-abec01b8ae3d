import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Question, questionService } from '@/services/questionService';
import { useLocalSearchParams, router } from 'expo-router';

export default function QuestionEditScreen() {
  const { id } = useLocalSearchParams();
  const [questionText, setQuestionText] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchQuestion = async () => {
        try {
          const data = await questionService.getQuestion(id as string);
          setQuestionText(data.text);
          setChapterId(data.chapterId);
        } catch (err) {
          setError('Failed to fetch question details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchQuestion();
    } else {
      setError('Question ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateQuestion = async () => {
    if (!questionText.trim() || !chapterId.trim()) {
      Alert.alert('Validation Error', 'Question text and Chapter ID cannot be empty.');
      return;
    }
    if (!id) {
      Alert.alert('Error', 'Question ID is missing.');
      return;
    }

    setSaving(true);
    try {
      await questionService.updateQuestion(id as string, { text: questionText, chapterId });
      Alert.alert('Success', 'Question updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update question. Please try again.');
      console.error('Failed to update question:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading question for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Edit Question</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Question Text"
        value={questionText}
        onChangeText={setQuestionText}
        editable={!saving}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="Chapter ID"
        value={chapterId}
        onChangeText={setChapterId}
        editable={!saving}
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateQuestion}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
