import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Session, sessionService } from '@/services/sessionService';
import { useLocalSearchParams, router } from 'expo-router';

export default function SessionEditScreen() {
  const { id } = useLocalSearchParams();
  const [userId, setUserId] = useState('');
  const [examId, setExamId] = useState('');
  const [score, setScore] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchSession = async () => {
        try {
          const data = await sessionService.getSession(id as string);
          setUserId(data.userId);
          setExamId(data.examId);
          setScore(data.score.toString());
        } catch (err) {
          setError('Failed to fetch session details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchSession();
    } else {
      setError('Session ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateSession = async () => {
    if (!userId.trim() || !examId.trim() || !score.trim()) {
      Alert.alert('Validation Error', 'User ID, Exam ID, and Score cannot be empty.');
      return;
    }
    if (!id) {
      Alert.alert('Error', 'Session ID is missing.');
      return;
    }

    setSaving(true);
    try {
      await sessionService.updateSession(id as string, { userId, examId, score: parseInt(score) });
      Alert.alert('Success', 'Session updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update session. Please try again.');
      console.error('Failed to update session:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading session for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Edit Session</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="User ID"
        value={userId}
        onChangeText={setUserId}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Exam ID"
        value={examId}
        onChangeText={setExamId}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Score"
        value={score}
        onChangeText={setScore}
        editable={!saving}
        keyboardType="numeric"
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateSession}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
