import React, { useEffect, useState } from 'react';
import { FlatList, ActivityIndicator, StyleSheet, Pressable, RefreshControl } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Course, courseService } from '@/services/courseService';
import { Link, router } from 'expo-router';

export default function CoursesListScreen() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCourses = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await courseService.getCourses();
      setCourses(data);
    } catch (err) {
      setError('Failed to fetch courses.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await courseService.getCourses();
      setCourses(data);
    } catch (err) {
      setError('Failed to refresh courses.');
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadCourses();
  }, []);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading courses...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Courses</ThemedText>
      <Link href="/screens/CourseAdd" asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>افزودن درس جدید</ThemedText>
        </Pressable>
      </Link>
      {courses.length === 0 ? (
        <ThemedText style={styles.noDataText}>درسی یافت نشد.</ThemedText>
      ) : (
        <FlatList
          data={courses}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ThemedView style={styles.courseItem}>
              <Link href={`/screens/CourseDetails?id=${item.id}`} asChild>
                <Pressable>
                  <ThemedText style={styles.courseName}>{item.name}</ThemedText>
                </Pressable>
              </Link>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/CourseEdit?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>ویرایش</ThemedText>
                  </Pressable>
                </Link>
                <Pressable
                  style={[styles.actionButton, styles.chaptersButton]}
                  onPress={() => router.push(`/screens/ChaptersList?courseId=${item.id}`)}
                >
                  <ThemedText style={styles.actionButtonText}>فصل‌ها</ThemedText>
                </Pressable>
                {/* Delete functionality would be implemented here */}
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#007bff']}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 16,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  courseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  courseName: {
    fontSize: 18,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: '#28a745',
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: '#fff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  chaptersButton: {
    backgroundColor: '#007bff', // Example color, adjust as needed
  },
});
