import React, { useEffect, useState } from 'react';
import { FlatList, ActivityIndicator, StyleSheet, Pressable, Alert, RefreshControl } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Chapter, chapterService } from '@/services/chapterService';
import { Link, useLocalSearchParams, router } from 'expo-router';

export default function ChaptersListScreen() {
  const { courseId } = useLocalSearchParams();
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadChapters = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await chapterService.getChapters();
      // Client-side filtering if courseId is provided
      if (courseId) {
        const numericCourseId = Array.isArray(courseId) ? parseInt(courseId[0]) : parseInt(courseId);
        const filteredChapters = data.filter(chapter => chapter.course_id === numericCourseId);
        setChapters(filteredChapters);
      } else {
        setChapters(data);
      }
    } catch (err) {
      setError('Failed to fetch chapters.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await chapterService.getChapters();
      // Client-side filtering if courseId is provided
      if (courseId) {
        const numericCourseId = Array.isArray(courseId) ? parseInt(courseId[0]) : parseInt(courseId);
        const filteredChapters = data.filter(chapter => chapter.course_id === numericCourseId);
        setChapters(filteredChapters);
      } else {
        setChapters(data);
      }
    } catch (err) {
      setError('Failed to refresh chapters.');
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadChapters();
  }, [courseId]); // Re-fetch when courseId changes

  const handleDeleteChapter = async (id: string) => {
    Alert.alert(
      'Delete Chapter',
      'Are you sure you want to delete this chapter?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              await chapterService.deleteChapter(id);
              Alert.alert('Success', 'Chapter deleted successfully!');
              loadChapters(); // Refresh the list
            } catch (error) {
              Alert.alert('Error', 'Failed to delete chapter. Please try again.');
              console.error('Failed to delete chapter:', error);
            }
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading chapters...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Chapters {courseId ? `for Course ${courseId}` : ''}</ThemedText>
      <Link href={`/screens/ChapterAdd${courseId ? `?courseId=${courseId}` : ''}`} asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>Add New Chapter</ThemedText>
        </Pressable>
      </Link>
      {chapters.length === 0 ? (
        <ThemedText style={styles.noDataText}>No chapters found.</ThemedText>
      ) : (
        <FlatList
          data={chapters}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ThemedView style={styles.chapterItem}>
              <Pressable onPress={() => router.push(`/ChapterDetails?id=${item.id}`)}>
                <ThemedText style={styles.chapterName}>{item.name}</ThemedText>
                {item.course_id && <ThemedText>Course ID: {item.course_id}</ThemedText>}
              </Pressable>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/ChapterEdit?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>ویرایش</ThemedText>
                  </Pressable>
                </Link>
                <Pressable
                  style={[styles.actionButton, styles.deleteButton]}
                  onPress={() => handleDeleteChapter(item.id)}
                >
                  <ThemedText style={styles.actionButtonText}>Delete</ThemedText>
                </Pressable>
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#007bff']}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 16,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  chapterItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  chapterName: {
    fontSize: 18,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: '#28a745',
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: '#fff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  deleteButton: {
    backgroundColor: '#dc3545',
  },
});
