import { majorService } from "@/services/majorService";
import React, { useState, useMemo } from "react";
import { rtlStyle } from "@/utils/rtl";
import {
  validateMajorForm,
  sanitizeInput,
  getCharacterCount,
} from "@/utils/validation";
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Path, Svg } from "react-native-svg";
import { router } from "expo-router";

// --- Icon Components ---

const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

// --- Main Screen Component ---

const ExamAddScreen = () => {
  const [name, setName] = useState<string>("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [nameError, setNameError] = useState("");
  const [descriptionError, setDescriptionError] = useState("");

  // Compute validation state
  const validation = useMemo(() => {
    return validateMajorForm(name, description);
  }, [name, description]);

  // Update error states when validation changes
  React.useEffect(() => {
    setNameError(validation.name.error || "");
    setDescriptionError(validation.description.error || "");
  }, [validation]);

  // Handle name change with validation
  const handleNameChange = (text: string) => {
    setName(text);
    if (nameError) {
      setNameError("");
    }
  };

  // Handle description change with validation
  const handleDescriptionChange = (text: string) => {
    setDescription(text);
    if (descriptionError) {
      setDescriptionError("");
    }
  };

  const handleSubmit = async () => {
    // Validate form before submission
    if (!validation.isFormValid) {
      Alert.alert("خطا", "لطفاً خطاهای فرم را برطرف کنید");
      return;
    }

    try {
      setLoading(true);
      await majorService.createMajor({
        name: sanitizeInput(name),
        description: sanitizeInput(description),
      });

      Alert.alert("موفقیت", "رشته تحصیلی ایجاد شد!", [
        {
          text: "تأیید",
          onPress: async () => {
            router.back();
          },
        },
      ]);
    } catch (err) {
      console.error("Failed to create major:", err);
      Alert.alert("خطا", (err as Error).message || "خطا در ایجاد رشته");
    } finally {
      setLoading(false);
    }
  };

  const handleBack = async () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
            <BackArrowIcon />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>افزودن رشته تحصیلی جدید</Text>
        </View>

        {/* Form Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.formContainer}
        >
          <View style={styles.inputContainer}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>نام رشته *</Text>
              <Text style={styles.characterCount}>
                {getCharacterCount(name)}/100
              </Text>
            </View>
            <TextInput
              style={[styles.input, nameError ? styles.inputError : null]}
              value={name}
              onChangeText={handleNameChange}
              placeholder="نام رشته را وارد کنید"
              placeholderTextColor="#9CA3AF"
              editable={!loading}
              maxLength={100}
            />
            {nameError ? (
              <Text style={styles.errorText}>{nameError}</Text>
            ) : null}
          </View>

          <View style={styles.inputContainer}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>توضیحات</Text>
              <Text style={styles.characterCount}>
                {getCharacterCount(description)}/510
              </Text>
            </View>
            <TextInput
              style={[
                styles.input,
                styles.textArea,
                descriptionError ? styles.inputError : null,
              ]}
              value={description}
              onChangeText={handleDescriptionChange}
              placeholder="توضیحات را وارد کنید"
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              editable={!loading}
              maxLength={500}
            />
            {descriptionError ? (
              <Text style={styles.errorText}>{descriptionError}</Text>
            ) : null}
            d1
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              (loading || !validation.isFormValid) &&
                styles.submitButtonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={loading || !validation.isFormValid}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.submitButtonText}>ایجاد</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

// --- Stylesheet ---

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
    direction: "rtl",
    textAlign: "right",
    fontFamily: "Vazirmatn",
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6", // gray-100
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1F2937", // gray-800
    ...rtlStyle.marginLeft(8),
    textAlign: rtlStyle.textAlign.start,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
  },
  characterCount: {
    fontSize: 12,
    color: "#6B7280", // gray-500
  },
  input: {
    borderWidth: 1,
    borderColor: "#D1D5DB",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: "#1F2937",
    backgroundColor: "#FFFFFF",
    textAlign: rtlStyle.textAlign.start,
  },
  inputError: {
    borderColor: "#EF4444", // red-500
    backgroundColor: "#FEF2F2", // red-50
  },
  errorText: {
    fontSize: 14,
    color: "#EF4444", // red-500
    marginTop: 4,
    textAlign: rtlStyle.textAlign.start,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  submitButton: {
    backgroundColor: "#2563EB",
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },
  submitButtonDisabled: {
    backgroundColor: "#9CA3AF",
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ExamAddScreen;
