import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, Switch } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { optionService } from '@/services/optionService';
import { router } from 'expo-router';

export default function OptionAddScreen() {
  const [text, setText] = useState('');
  const [questionId, setQuestionId] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleAddOption = async () => {
    if (!text.trim() || !questionId.trim()) {
      Alert.alert('Validation Error', 'Option text and Question ID cannot be empty.');
      return;
    }

    setLoading(true);
    try {
      // Assuming your CreateOptionDto has 'text', 'questionId', and 'isCorrect' fields
      await optionService.createOption({ text, questionId, isCorrect });
      Alert.alert('Success', 'Option added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add option. Please try again.');
      console.error('Failed to add option:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Add New Option</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Option Text"
        value={text}
        onChangeText={setText}
        editable={!loading}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="Question ID"
        value={questionId}
        onChangeText={setQuestionId}
        editable={!loading}
      />
      <ThemedView style={styles.switchContainer}>
        <ThemedText style={styles.label}>Is Correct:</ThemedText>
        <Switch
          value={isCorrect}
          onValueChange={setIsCorrect}
          disabled={loading}
        />
      </ThemedView>
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddOption}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'Adding...' : 'Add Option'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    justifyContent: 'space-between',
  },
  label: {
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
