import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { questionService } from '@/services/questionService';
import { router } from 'expo-router';

export default function QuestionAddScreen() {
  const [questionText, setQuestionText] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddQuestion = async () => {
    if (!questionText.trim() || !chapterId.trim()) {
      Alert.alert('Validation Error', 'Question text and Chapter ID cannot be empty.');
      return;
    }

    setLoading(true);
    try {
      // Assuming your CreateQuestionDto has 'text' and 'chapterId' fields
      await questionService.createQuestion({ text: questionText, chapterId });
      Alert.alert('Success', 'Question added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add question. Please try again.');
      console.error('Failed to add question:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Add New Question</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Question Text"
        value={questionText}
        onChangeText={setQuestionText}
        editable={!loading}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="Chapter ID"
        value={chapterId}
        onChangeText={setChapterId}
        editable={!loading}
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddQuestion}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'Adding...' : 'Add Question'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
