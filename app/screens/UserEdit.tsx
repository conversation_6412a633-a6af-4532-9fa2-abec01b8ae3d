import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { User, userService } from '@/services/userService';
import { useLocalSearchParams, router } from 'expo-router';

export default function UserEditScreen() {
  const { id } = useLocalSearchParams();
  const [email, setEmail] = useState('');
  const [mobile, setMobile] = useState('');
  const [passwordHash, setPasswordHash] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchUser = async () => {
        try {
          const data = await userService.getUser(id as string);
          setEmail(data.email);
          setMobile(data.mobile);
          // password_hash is not typically fetched for security reasons,
          // but we keep the state for potential updates.
          setFirstName(data.first_name);
          setLastName(data.last_name);
          setRole(data.role);
        } catch (err) {
          setError('Failed to fetch user details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchUser();
    } else {
      setError('User ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateUser = async () => {
    if (!email.trim() || !mobile.trim() || !firstName.trim() || !lastName.trim() || !role.trim()) {
      Alert.alert('Validation Error', 'All fields except Password Hash are required.');
      return;
    }
    if (!id) {
      Alert.alert('Error', 'User ID is missing.');
      return;
    }

    setSaving(true);
    try {
      const userData: Partial<User> = {
        email,
        mobile,
        first_name: firstName,
        last_name: lastName,
        role,
      };
      if (passwordHash.trim()) {
        userData.password_hash = passwordHash;
      }

      await userService.updateUser(id as string, userData);
      Alert.alert('Success', 'User updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update user. Please try again.');
      console.error('Failed to update user:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading user for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Edit User</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        editable={!saving}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        style={styles.input}
        placeholder="Mobile"
        value={mobile}
        onChangeText={setMobile}
        editable={!saving}
        keyboardType="phone-pad"
      />
      <TextInput
        style={styles.input}
        placeholder="Password (leave blank to keep current)"
        value={passwordHash}
        onChangeText={setPasswordHash}
        editable={!saving}
        secureTextEntry
      />
      <TextInput
        style={styles.input}
        placeholder="First Name"
        value={firstName}
        onChangeText={setFirstName}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Last Name"
        value={lastName}
        onChangeText={setLastName}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Role"
        value={role}
        onChangeText={setRole}
        editable={!saving}
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateUser}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
