import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Chapter, chapterService } from '@/services/chapterService';
import { useLocalSearchParams } from 'expo-router';

export default function ChapterDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchChapter = async () => {
        try {
          const data = await chapterService.getChapter(id as string);
          setChapter(data);
        } catch (err) {
          setError('Failed to fetch chapter details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchChapter();
    } else {
      setError('Chapter ID not provided.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading chapter details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!chapter) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>Chapter not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Chapter Details</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>ID:</ThemedText>
        <ThemedText>{chapter.id}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>Name:</ThemedText>
        <ThemedText>{chapter.name}</ThemedText>
      </ThemedView>
      {chapter.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>{new Date(chapter.createdAt).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
