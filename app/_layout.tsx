import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { I18nManager } from 'react-native';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    Vazirmatn: require("../assets/fonts/vazirmatn/Vazirmatn-Regular.ttf"),
  });

  // Enable RTL layout
  I18nManager.allowRTL(true);
  I18nManager.forceRTL(true);
    
  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack>
        {/* <Stack.Screen name="Home" options={{ headerShown: false }} />
        <Stack.Screen name="about" options={{ headerShown: false }} /> */}
        <Stack.Screen name="(drawer)" options={{ headerShown: false }} />
        {/* <Stack.Screen name="(tabs)" options={{ headerShown: false }} /> */}
        {/* <Stack.Screen name="ExamAdd" options={{ headerShown: false }} />
        <Stack.Screen name="ExamEdit" options={{ headerShown: false }} />
        <Stack.Screen name="MajorDetails" options={{ headerShown: false }} />
        <Stack.Screen name="MajorAdd" options={{ headerShown: false }} />
        <Stack.Screen name="MajorEdit" options={{ headerShown: false }} />
        <Stack.Screen name="CourseDetails" options={{ headerShown: false }} />
        <Stack.Screen name="CourseAdd" options={{ headerShown: false }} />
        <Stack.Screen name="CourseEdit" options={{ headerShown: false }} />
        <Stack.Screen name="ChapterDetails" options={{ headerShown: false }} />
        <Stack.Screen name="ChapterAdd" options={{ headerShown: false }} />
        <Stack.Screen name="ChapterEdit" options={{ headerShown: false }} />
        <Stack.Screen name="MajorCourseDetails" options={{ headerShown: false }} />
        <Stack.Screen name="MajorCourseAdd" options={{ headerShown: false }} />
        <Stack.Screen name="MajorCourseEdit" options={{ headerShown: false }} />
        <Stack.Screen name="QuestionDetails" options={{ headerShown: false }} />
        <Stack.Screen name="QuestionAdd" options={{ headerShown: false }} />
        <Stack.Screen name="QuestionEdit" options={{ headerShown: false }} />
        <Stack.Screen name="OptionDetails" options={{ headerShown: false }} />
        <Stack.Screen name="OptionAdd" options={{ headerShown: false }} />
        <Stack.Screen name="OptionEdit" options={{ headerShown: false }} />
        <Stack.Screen name="UserDetails" options={{ headerShown: false }} />
        <Stack.Screen name="UserAdd" options={{ headerShown: false }} />
        <Stack.Screen name="UserEdit" options={{ headerShown: false }} />
        <Stack.Screen name="SessionDetails" options={{ headerShown: false }} />
        <Stack.Screen name="SessionAdd" options={{ headerShown: false }} />
        <Stack.Screen name="SessionEdit" options={{ headerShown: false }} /> */}
        {/* <Stack.Screen name="+not-found" /> */}
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}
