import { Drawer } from "expo-router/drawer";
import React from "react";
import { Platform, I18nManager, Text } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import { useColorScheme } from "@/hooks/useColorScheme";
import { Colors } from "@/constants/Colors";
import { CustomDrawerContent } from "@/components/CustomDrawerContent";

export default function DrawerLayout() {
  const colorScheme = useColorScheme();

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={CustomDrawerContent}
        screenOptions={{
          headerShown: false,
          drawerPosition: I18nManager.isRTL ? "right" : "left",
          drawerType: Platform.select({
            ios: "slide",
            android: "front",
            default: "front",
          }),
          drawerStyle: {
            backgroundColor: Colors[colorScheme ?? "light"].background,
            width: 280,
          },
          drawerActiveTintColor: Colors[colorScheme ?? "light"].tint,
          drawerInactiveTintColor:
            Colors[colorScheme ?? "light"].tabIconDefault,
          drawerLabelStyle: {
            fontFamily: "Vazirmatn",
            fontSize: 1,
            textAlign: I18nManager.isRTL ? "right" : "left",
          },
        }}
      />
    </GestureHandlerRootView>
  );
}
